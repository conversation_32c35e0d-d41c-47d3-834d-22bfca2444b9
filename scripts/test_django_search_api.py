#!/usr/bin/env python
"""
Django Search API Comprehensive Testing Script

This script tests the Django Search API with all RAG techniques:
1. Hybrid Search (BM25 + Vector)
2. Query Expansion (HyDE)
3. Multi-Step Reasoning
4. Context-Aware Retrieval
5. Citation Engine
6. Router Engine

Author: AI Assistant
Date: 2025-01-30
"""

import os
import sys
import requests
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DjangoSearchAPITester:
    """
    Comprehensive tester for Django Search API with all RAG techniques.
    """

    def __init__(self, base_url: str = "http://localhost:8000", tenant_slug: str = "test-tenant"):
        self.base_url = base_url
        self.tenant_slug = tenant_slug
        self.api_endpoint = f"{base_url}/api/search/"
        self.test_results = {
            'total_tests': 0,
            'successful_tests': 0,
            'failed_tests': 0,
            'test_details': [],
            'rag_techniques_verified': {
                'hybrid_search': False,
                'query_expansion': False,
                'multi_step_reasoning': False,
                'context_aware': False,
                'citation_engine': False,
                'router_engine': False
            },
            'performance_metrics': {
                'total_time': 0.0,
                'average_time': 0.0,
                'min_time': float('inf'),
                'max_time': 0.0
            }
        }

    def check_server_availability(self) -> bool:
        """Check if Django server is running and accessible."""
        logger.info("🔍 Checking Django server availability...")
        
        try:
            response = requests.get(f"{self.base_url}/admin/", timeout=5)
            if response.status_code in [200, 302]:  # 302 for redirect to login
                logger.info("✅ Django server is accessible")
                return True
            else:
                logger.warning(f"⚠️ Django server responded with status {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Django server is not accessible: {e}")
            return False

    def test_basic_search(self) -> Dict[str, Any]:
        """Test basic search functionality."""
        logger.info("🔍 Testing basic search...")
        
        test_data = {
            'name': 'Basic Search',
            'query': 'budget adherence testing',
            'params': {
                'use_hybrid_search': True,
                'use_context_aware': True,
                'use_query_expansion': False,
                'use_multi_step_reasoning': False,
                'top_k': 10
            }
        }
        
        return self._execute_test(test_data)

    def test_query_expansion(self) -> Dict[str, Any]:
        """Test query expansion with HyDE."""
        logger.info("🔍 Testing query expansion (HyDE)...")
        
        test_data = {
            'name': 'Query Expansion (HyDE)',
            'query': 'bug reports and issues',
            'params': {
                'use_hybrid_search': True,
                'use_context_aware': True,
                'use_query_expansion': True,
                'use_multi_step_reasoning': False,
                'top_k': 15
            }
        }
        
        return self._execute_test(test_data)

    def test_multi_step_reasoning(self) -> Dict[str, Any]:
        """Test multi-step reasoning."""
        logger.info("🔍 Testing multi-step reasoning...")
        
        test_data = {
            'name': 'Multi-Step Reasoning',
            'query': 'What are the main engineering challenges discussed in recent meetings?',
            'params': {
                'use_hybrid_search': True,
                'use_context_aware': True,
                'use_query_expansion': False,
                'use_multi_step_reasoning': True,
                'top_k': 20
            }
        }
        
        return self._execute_test(test_data)

    def test_full_rag_features(self) -> Dict[str, Any]:
        """Test all RAG features combined."""
        logger.info("🔍 Testing full RAG features...")
        
        test_data = {
            'name': 'Full RAG Features',
            'query': 'Summarize customer feedback and manager recommendations from the last quarter',
            'params': {
                'use_hybrid_search': True,
                'use_context_aware': True,
                'use_query_expansion': True,
                'use_multi_step_reasoning': True,
                'top_k': 25,
                'output_format': 'markdown'
            }
        }
        
        return self._execute_test(test_data)

    def test_low_relevance_threshold(self) -> Dict[str, Any]:
        """Test with low relevance threshold."""
        logger.info("🔍 Testing low relevance threshold...")
        
        test_data = {
            'name': 'Low Relevance Threshold',
            'query': 'testing updates and showstopper bugs',
            'params': {
                'use_hybrid_search': True,
                'use_context_aware': True,
                'use_query_expansion': True,
                'use_multi_step_reasoning': False,
                'min_relevance_score': 0.2,
                'top_k': 30
            }
        }
        
        return self._execute_test(test_data)

    def test_different_output_formats(self) -> List[Dict[str, Any]]:
        """Test different output formats."""
        logger.info("🔍 Testing different output formats...")
        
        formats = ['text', 'json', 'markdown', 'table']
        results = []
        
        for format_type in formats:
            test_data = {
                'name': f'Output Format: {format_type}',
                'query': 'manager recommendations',
                'params': {
                    'use_hybrid_search': True,
                    'use_context_aware': True,
                    'output_format': format_type,
                    'top_k': 10
                }
            }
            
            result = self._execute_test(test_data)
            results.append(result)
        
        return results

    def _execute_test(self, test_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single test case."""
        self.test_results['total_tests'] += 1
        
        try:
            # Prepare API request
            payload = {
                'query': test_data['query'],
                'tenant_slug': self.tenant_slug,
                **test_data['params']
            }
            
            # Measure execution time
            start_time = time.time()
            
            # Make API call
            response = requests.post(
                self.api_endpoint,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=60
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # Update performance metrics
            self._update_performance_metrics(execution_time)
            
            # Process response
            if response.status_code == 200:
                response_data = response.json()
                
                if response_data.get('status') == 'success' and 'data' in response_data:
                    self.test_results['successful_tests'] += 1
                    
                    # Track RAG techniques
                    self._track_rag_techniques(test_data['params'], response_data)
                    
                    result = {
                        'test_name': test_data['name'],
                        'status': 'success',
                        'execution_time': execution_time,
                        'query': test_data['query'],
                        'answer_length': len(response_data['data'].get('answer', '')),
                        'sources_count': len(response_data['data'].get('sources', [])),
                        'confidence_score': response_data['data'].get('metrics', {}).get('confidence_score', 0),
                        'retriever_score': response_data['data'].get('metrics', {}).get('retriever_score', 0),
                        'is_fallback': response_data['data'].get('metrics', {}).get('is_fallback', False),
                        'rag_params': test_data['params']
                    }
                    
                    logger.info(f"   ✅ {test_data['name']} - Time: {execution_time:.2f}s, Sources: {result['sources_count']}")
                    
                else:
                    self.test_results['failed_tests'] += 1
                    result = {
                        'test_name': test_data['name'],
                        'status': 'failed',
                        'error': 'Invalid response structure',
                        'execution_time': execution_time
                    }
                    logger.warning(f"   ❌ {test_data['name']} - Invalid response structure")
            else:
                self.test_results['failed_tests'] += 1
                result = {
                    'test_name': test_data['name'],
                    'status': 'failed',
                    'error': f"HTTP {response.status_code}: {response.text[:200]}",
                    'execution_time': execution_time
                }
                logger.error(f"   ❌ {test_data['name']} - HTTP {response.status_code}")
                
        except Exception as e:
            self.test_results['failed_tests'] += 1
            result = {
                'test_name': test_data['name'],
                'status': 'error',
                'error': str(e),
                'execution_time': 0
            }
            logger.error(f"   ❌ {test_data['name']} - Error: {e}")
        
        self.test_results['test_details'].append(result)
        return result

    def _update_performance_metrics(self, execution_time: float):
        """Update performance metrics."""
        self.test_results['performance_metrics']['total_time'] += execution_time
        self.test_results['performance_metrics']['min_time'] = min(
            self.test_results['performance_metrics']['min_time'], execution_time
        )
        self.test_results['performance_metrics']['max_time'] = max(
            self.test_results['performance_metrics']['max_time'], execution_time
        )
        
        if self.test_results['total_tests'] > 0:
            self.test_results['performance_metrics']['average_time'] = (
                self.test_results['performance_metrics']['total_time'] / self.test_results['total_tests']
            )

    def _track_rag_techniques(self, params: Dict[str, Any], response_data: Dict[str, Any]):
        """Track which RAG techniques were successfully used."""
        if params.get('use_hybrid_search'):
            self.test_results['rag_techniques_verified']['hybrid_search'] = True
        if params.get('use_query_expansion'):
            self.test_results['rag_techniques_verified']['query_expansion'] = True
        if params.get('use_multi_step_reasoning'):
            self.test_results['rag_techniques_verified']['multi_step_reasoning'] = True
        if params.get('use_context_aware'):
            self.test_results['rag_techniques_verified']['context_aware'] = True
        
        # Check for citations (indicates citation engine is working)
        sources_count = len(response_data['data'].get('sources', []))
        if sources_count > 0:
            self.test_results['rag_techniques_verified']['citation_engine'] = True
        
        # Check for router engine (indicated by successful routing)
        if response_data['data'].get('answer') and sources_count > 0:
            self.test_results['rag_techniques_verified']['router_engine'] = True

    def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run all comprehensive tests."""
        logger.info("🚀 Starting comprehensive Django Search API testing...")
        logger.info("=" * 80)
        
        # Check server availability
        if not self.check_server_availability():
            logger.error("❌ Cannot proceed without Django server")
            return self.test_results
        
        # Run individual tests
        self.test_basic_search()
        self.test_query_expansion()
        self.test_multi_step_reasoning()
        self.test_full_rag_features()
        self.test_low_relevance_threshold()
        
        # Test different output formats
        format_results = self.test_different_output_formats()
        
        # Generate final report
        self._generate_final_report()
        
        return self.test_results

    def _generate_final_report(self):
        """Generate and display final test report."""
        logger.info("=" * 80)
        logger.info("📊 FINAL TEST RESULTS")
        logger.info("=" * 80)
        
        logger.info(f"Total Tests: {self.test_results['total_tests']}")
        logger.info(f"Successful Tests: {self.test_results['successful_tests']}")
        logger.info(f"Failed Tests: {self.test_results['failed_tests']}")
        
        # Performance metrics
        metrics = self.test_results['performance_metrics']
        logger.info(f"\nPerformance Metrics:")
        logger.info(f"  - Total Time: {metrics['total_time']:.2f}s")
        logger.info(f"  - Average Time: {metrics['average_time']:.2f}s")
        logger.info(f"  - Min Time: {metrics['min_time']:.2f}s")
        logger.info(f"  - Max Time: {metrics['max_time']:.2f}s")
        
        # RAG techniques verification
        logger.info(f"\nRAG Techniques Verified:")
        for technique, verified in self.test_results['rag_techniques_verified'].items():
            status = "✅" if verified else "❌"
            logger.info(f"  - {technique}: {status}")
        
        # Overall status
        success_rate = (self.test_results['successful_tests'] / self.test_results['total_tests']) * 100
        if success_rate >= 90:
            logger.info(f"\n✅ EXCELLENT: {success_rate:.1f}% success rate")
        elif success_rate >= 70:
            logger.info(f"\n⚠️ GOOD: {success_rate:.1f}% success rate")
        else:
            logger.info(f"\n❌ NEEDS IMPROVEMENT: {success_rate:.1f}% success rate")


def main():
    """Main function to run the comprehensive API test."""
    print("🔬 Django Search API Comprehensive Testing")
    print("=" * 50)
    
    # You can customize these parameters
    base_url = "http://localhost:8000"
    tenant_slug = "test-tenant"
    
    tester = DjangoSearchAPITester(base_url=base_url, tenant_slug=tenant_slug)
    
    try:
        results = tester.run_comprehensive_test()
        
        # Save results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"../docs/api_test_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n📄 Results saved to: {filename}")
        
        # Return appropriate exit code
        success_rate = (results['successful_tests'] / results['total_tests']) * 100 if results['total_tests'] > 0 else 0
        return 0 if success_rate >= 70 else 1
        
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
